# XY商品采集器 - Python版本依赖包

# GUI框架
PyQt5>=5.15.0

# 浏览器自动化
DrissionPage>=4.0.0

# HTTP请求
requests>=2.25.0

# Excel处理
openpyxl>=3.0.0

# 数据处理
pandas>=1.3.0

# 图像处理
Pillow>=8.0.0

# 系统工具
psutil>=5.8.0

# 日期时间处理
python-dateutil>=2.8.0

# JSON处理（Python内置，但列出以备参考）
# json

# 正则表达式（Python内置）
# re

# 文件路径处理（Python内置）
# pathlib

# 临时文件（Python内置）
# tempfile

# ZIP文件处理（Python内置）
# zipfile

# 网页浏览器（Python内置）
# webbrowser

# 哈希算法（Python内置）
# hashlib

# 平台信息（Python内置）
# platform

# UUID生成（Python内置）
# uuid

# 时间处理（Python内置）
# time

# 操作系统接口（Python内置）
# os

# 日志记录（Python内置）
# logging

# 线程处理（Python内置）
# threading
