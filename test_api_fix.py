#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API修复效果
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_search_api():
    """测试搜索API"""
    # 设置日志
    logger = setup_logger()
    logger.info("开始测试搜索API修复效果")
    
    # 初始化配置管理器
    config_manager = ConfigManager()
    
    # 初始化采集引擎
    collector = CollectorEngine(config_manager)
    
    try:
        # 测试关键词
        test_keyword = "手机"
        
        logger.info(f"测试关键词: {test_keyword}")
        
        # 采集数据
        results = collector.collect_search_data(
            keyword=test_keyword,
            max_pages=1,  # 只测试第一页
            min_want_count=1,
            progress_callback=lambda progress: logger.info(f"进度: {progress}")
        )
        
        if results:
            logger.info(f"✅ 测试成功！获取到 {len(results)} 条数据")
            
            # 显示前3条数据
            for i, item in enumerate(results[:3]):
                logger.info(f"商品 {i+1}: {item.get('title', 'N/A')[:50]}...")
                logger.info(f"  价格: {item.get('price', 'N/A')}")
                logger.info(f"  想要人数: {item.get('wantCount', 'N/A')}")
                
        else:
            logger.warning("❌ 测试失败：未获取到任何数据")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        
    finally:
        # 清理资源
        if hasattr(collector, 'page') and collector.page:
            collector.page.close()
        logger.info("测试完成")

if __name__ == "__main__":
    test_search_api()
