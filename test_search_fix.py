#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索API修复效果
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.collector_engine import CollectorEngine
from core.config_manager import ConfigManager
from utils.logger import setup_logger

def test_search_api():
    """测试搜索API修复效果"""
    # 设置日志
    logger = setup_logger()
    logger.info("🧪 开始测试搜索API修复效果")

    # 初始化配置管理器
    config_manager = ConfigManager()

    # 初始化采集引擎
    collector = CollectorEngine(config_manager)

    try:
        # 检查cookies状态
        if collector.has_valid_cookies():
            logger.info("✅ 检测到有效的登录cookies")

            # 显示cookies详情
            try:
                import json
                with open('cookies.json', 'r', encoding='utf-8') as f:
                    cookie_data = json.load(f)
                    cookies = cookie_data.get('cookies', {})
                    logger.info(f"📊 Cookies总数: {len(cookies)}")

                    # 检查核心cookies
                    core_cookies = ['_m_h5_tk', '_tb_token_', 't']
                    for cookie_name in core_cookies:
                        if cookie_name in cookies:
                            logger.info(f"✅ 核心Cookie {cookie_name}: 存在")
                        else:
                            logger.warning(f"❌ 核心Cookie {cookie_name}: 缺失")
            except Exception as e:
                logger.warning(f"读取cookies详情失败: {e}")
        else:
            logger.error("❌ 未找到有效的登录cookies，请先登录")
            return
        
        # 测试关键词
        test_keyword = "手机"
        
        logger.info(f"🔍 测试关键词: {test_keyword}")
        
        # 定义进度回调
        def progress_callback(progress):
            logger.info(f"📊 进度更新: 第{progress['current_page']}/{progress['total_pages']}页, "
                       f"当前页{progress['current_items']}条, 总计{progress['total_items']}条")
        
        # 采集数据
        results = collector.collect_search_data(
            keyword=test_keyword,
            max_pages=2,  # 测试2页
            min_want_count=1,
            progress_callback=progress_callback
        )
        
        if results:
            logger.info(f"✅ 测试成功！获取到 {len(results)} 条数据")
            
            # 显示前3条数据的详细信息
            for i, item in enumerate(results[:3]):
                logger.info(f"\n📱 商品 {i+1}:")
                logger.info(f"  标题: {item.get('title', 'N/A')}")
                logger.info(f"  价格: {item.get('price', 'N/A')}")
                logger.info(f"  想要人数: {item.get('wantCount', 'N/A')}")
                logger.info(f"  链接: {item.get('link', 'N/A')[:50]}...")
                
        else:
            logger.warning("❌ 测试失败：未获取到任何数据")
            logger.info("💡 可能的原因：")
            logger.info("   1. 服务器过载，请稍后重试")
            logger.info("   2. 登录状态已过期，请重新登录")
            logger.info("   3. API参数需要调整")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        
    finally:
        # 清理资源
        try:
            collector.close()
        except:
            pass
        logger.info("🔚 测试完成")

if __name__ == "__main__":
    test_search_api()
